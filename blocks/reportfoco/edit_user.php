<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file management
 *
 * @package    block_reportfoco
 * @copyright  2024 Raphael Enes <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require ('../../config.php');

require_login();

// Define a URL base para este script
$url = new moodle_url('/blocks/reportfoco/index.php');
$cancelurl = new moodle_url('/blocks/reportfoco/');
$context = context_system::instance();
$PAGE->set_url($url);
$PAGE->set_context(context_system::instance());
$PAGE->set_title(get_string('pluginname', 'block_reportfoco'));
$PAGE->set_heading(get_string('pluginname', 'block_reportfoco'));

$id = optional_param('id', 0, PARAM_INT);
$mes_escolhido = optional_param('mes', 0, PARAM_INT);
$sesskey = optional_param('sesskey', '', PARAM_ALPHANUMEXT);


global $DB;

require_once ('reportfocoadduser_form.php');

$sql = "SELECT f.*, q.*
        FROM {block_reportfoco_funcoes} f
        LEFT JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao
        WHERE f.id = :id AND q.mes = :mes_escolhido";

$params = ['id' => $id, 'mes_escolhido' => $mes_escolhido];
$record = $DB->get_record_sql($sql, $params);

if($record){
    $record = $DB->get_record_sql($sql, $params);
}else{
    $record = $DB->get_record('block_reportfoco_funcoes', array('id' => $id));
}

$mform = new reportfocoAdduser_form();
$formdata = new stdClass();
if ($record) {
    // Preencha $formdata com os valores obtidos, para usar no set_data.
    $formdata->id = $record->id;
    $formdata->name = $record->name;
    $formdata->questao = !empty ($record->questao);
    $formdata->questao_valor = $record->questao_valor;
    $formdata->correcao_questao = !empty ($record->correcao_questao);
    $formdata->correcao_questao_valor = $record->correcao_questao_valor;

    // Se tiver userid, busca o e-mail do usuário
    if (!empty($record->userid)) {
        $user = $DB->get_record('user', ['id' => $record->userid], 'email');
        if ($user) {
            $formdata->correcao_questao_email = $user->email;
        }
    }
    $formdata->redacao = !empty ($record->redacao);
    $formdata->redacao_valor = $record->redacao_valor;
    $formdata->quimica = !empty ($record->quimica);
    $formdata->quimica_valor = $record->quimica_valor;
    $formdata->biologia = !empty ($record->biologia);
    $formdata->biologia_valor = $record->biologia_valor;
    // $formdata->professor = !empty ($record->professor);
    // $formdata->professor_valor = $record->professor_valor;
    $formdata->assistente = !empty ($record->assistente);
    $formdata->assistente_valor = $record->assistente_valor;
    $formdata->monitor = !empty ($record->monitor);
    $formdata->monitor_valor = $record->monitor_valor;
    $formdata->mchat = !empty ($record->mchat);
    $formdata->mchat_valor = $record->mchat_valor;
    $formdata->estudio = !empty ($record->estudio);
    $formdata->estudio_valor = $record->estudio_valor;
    $formdata->aplicador = !empty ($record->aplicador);
    $formdata->aplicador_valor = $record->aplicador_valor;
    $formdata->colaborador = !empty ($record->colaborador);
    $formdata->colaborador_valor = $record->colaborador_valor;
    $formdata->colaboradorserv = !empty ($record->colaboradorserv);
    $formdata->colaboradorserv_valor = $record->colaboradorserv_valor;
    $formdata->semibarra = !empty($record->semibarra);
    $formdata->semibarra_valor = $record->semibarra_valor;
    $formdata->semitijuca = !empty($record->semitijuca);
    $formdata->semitijuca_valor = $record->semitijuca_valor;
    $formdata->profonline = !empty($record->profonline);
    $formdata->profonline_valor = $record->profonline_valor;
    $formdata->profprojetos = !empty($record->profprojetos);
    $formdata->profprojetos_valor = $record->profprojetos_valor;
    $formdata->obs = $record->obs;
    $formdata->funcao_usr = $record->funcao_usr;
}

$mform->set_data($formdata);

if ($mform->is_cancelled()) {
    // Handle form cancellation.
    redirect($cancelurl);
} else if ($fromform = $mform->get_data()) {
    // Lista dos campos das funções que precisam ser verificados
    $fields = [
        'questao',
        'correcao_questao',
        'redacao',
        'quimica',
        'biologia',
        'professor',
        'assistente',
        'monitor',
        'estudio',
        'aplicador',
        'colaborador',
        'colaboradorserv',
        'mchat',
        'semibarra',
        'semitijuca',
        'profonline',
        'profprojetos'
    ];

    foreach ($fields as $field) {
        // Verifica se a função está definida como 0 (Inativo)
        // Se sim, ajusta o valor correspondente para NULL
        if (isset ($fromform->{$field}) && $fromform->{$field} == 0) {
            $fromform->{$field . '_valor'} = null;
        }
    }

    // Certifique-se de definir o ID para atualizar o registro correto
    $fromform->id = $id;

    // Se a função "Correção Questão" estiver ativa e o e-mail foi informado, busca o userid
    if ($fromform->correcao_questao == 1 && !empty($fromform->correcao_questao_email)) {
        // Busca o usuário pelo e-mail
        $user = $DB->get_record('user', ['email' => $fromform->correcao_questao_email], 'id');
        if ($user) {
            $fromform->userid = $user->id;
        } else {
            // Se não encontrar o usuário, exibe uma mensagem de aviso
            $urlredirect = new moodle_url('/blocks/reportfoco/index.php');
            redirect($urlredirect, 'Usuário com o e-mail ' . $fromform->correcao_questao_email . ' não encontrado no sistema. O registro foi atualizado, mas sem associação com um usuário.', null, \core\output\notification::NOTIFY_WARNING);
        }
    } else if ($fromform->correcao_questao == 0) {
        // Se a função estiver inativa, remove o userid
        $fromform->userid = null;
    }

    $DB->update_record('block_reportfoco_funcoes', $fromform);

    // Define URL de redirecionamento
    $urlredirect = new moodle_url('/blocks/reportfoco/index.php');

    $params['id'] = $fromform->id;
    $sql = "SELECT * FROM  {block_reportfoco_funcoes} f
    WHERE f.id = ?";
    $dadosduncao = $DB->get_record_sql($sql, $params);

    if ($dadosduncao->colaboradorserv == 1) {

        $params['id'] = $dadosduncao->id;
        $sql = "SELECT f.*, q.* FROM  {block_reportfoco_funcoes} f
        left JOIN {block_reportfoco_qtd} q ON f.id=q.idfuncao
        WHERE f.id = ?";
        $dadosduncaoqtd = $DB->get_record_sql($sql, $params);

        // Lógica para UPDATE.
        $mes = date('n'); // Supondo que você quer limitar o update ao registro do mês atual.
        $record = new stdClass();
        $record->id = $dadosduncaoqtd->id;
        $record->mes = $dadosduncaoqtd->mes;
        $record->idfuncao = $dadosduncao->id;
        $record->qtd = 1;
        $record->valor_mes = $dadosduncaoqtd->colaboradorserv_valor;

        if($mes <> $record->mes) {
            $mes = date('n');
            $record = new stdClass();
            $record->idfuncao = $dadosduncao->id;
            $record->qtd = $dadosduncao->colaboradorserv;
            $record->funcao = 'colaboradorserv';
            $record->timecreated = time();
            $record->mes = $mes;
            $record->valor_mes = $dadosduncao->colaboradorserv_valor;

            $DB->insert_record('block_reportfoco_qtd', $record);
        }else{
            $DB->update_record('block_reportfoco_qtd', $record);
        }


    } else if ($dadosduncao->colaboradorserv == 0) {

        $mes = date('n');
        $record = new stdClass();
        $record->idfuncao = $dadosduncao->id;
        $record->qtd = $dadosduncao->colaboradorserv;
        $record->funcao = 'colaboradorserv';
        $record->timecreated = time();
        $record->mes = $mes;
        $record->valor_mes = $dadosduncao->colaboradorserv_valor;

        $DB->insert_record('block_reportfoco_qtd', $record);
    }

    // Lógica similar para o campo "colaborador"
    if ($dadosduncao->colaborador == 1) {

        $params['id'] = $dadosduncao->id;
        $sql = "SELECT f.*, q.* FROM  {block_reportfoco_funcoes} f
        left JOIN {block_reportfoco_qtd} q ON f.id=q.idfuncao AND q.funcao = 'colaborador'
        WHERE f.id = ?";
        $dadosduncaoqtd = $DB->get_record_sql($sql, $params);

        // Lógica para UPDATE.
        $mes = date('n'); // Supondo que você quer limitar o update ao registro do mês atual.

        if ($dadosduncaoqtd && !empty($dadosduncaoqtd->mes)) {
            $record = new stdClass();
            $record->id = $dadosduncaoqtd->id;
            $record->mes = $dadosduncaoqtd->mes;
            $record->idfuncao = $dadosduncao->id;
            $record->qtd = 1;
            $record->valor_mes = $dadosduncao->colaborador_valor;

            if($mes <> $record->mes) {
                $mes = date('n');
                $record = new stdClass();
                $record->idfuncao = $dadosduncao->id;
                $record->qtd = $dadosduncao->colaborador;
                $record->funcao = 'colaborador';
                $record->timecreated = time();
                $record->mes = $mes;
                $record->valor_mes = $dadosduncao->colaborador_valor;

                $DB->insert_record('block_reportfoco_qtd', $record);
            } else {
                $DB->update_record('block_reportfoco_qtd', $record);
            }
        } else {
            // Se não existe registro na tabela qtd, cria um novo
            $mes = date('n');
            $record = new stdClass();
            $record->idfuncao = $dadosduncao->id;
            $record->qtd = $dadosduncao->colaborador;
            $record->funcao = 'colaborador';
            $record->timecreated = time();
            $record->mes = $mes;
            $record->valor_mes = $dadosduncao->colaborador_valor;

            $DB->insert_record('block_reportfoco_qtd', $record);
        }

    } else if ($dadosduncao->colaborador == 0) {

        $mes = date('n');
        $record = new stdClass();
        $record->idfuncao = $dadosduncao->id;
        $record->qtd = $dadosduncao->colaborador;
        $record->funcao = 'colaborador';
        $record->timecreated = time();
        $record->mes = $mes;
        $record->valor_mes = $dadosduncao->colaborador_valor;

        $DB->insert_record('block_reportfoco_qtd', $record);
    }

    // Redirecionamento após o processamento para evitar reenvios do formulário
    redirect($urlredirect, 'Dados salvos com sucesso!', null, \core\output\notification::NOTIFY_SUCCESS);
} else {
    // Display the form.
    echo $OUTPUT->header();
    $mform->display();
    echo $OUTPUT->footer();
}
