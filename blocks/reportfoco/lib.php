<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Callback implementations for Report ADM
 *
 * @package    block_reportfoco
 * @copyright  2024 Raphael Enes <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once ($CFG->libdir . '/tablelib.php');


class reportfoco_funcoes_table extends table_sql
{
    private $mes;
    private $ano;
    private $exfuncao;
    private $user_totals = [];
    private $total_acumulado = 0;
    function __construct($uniqueid, $mes = null, $ano_escolhido = null, $exfuncao_escolhido = null)
    {
        parent::__construct($uniqueid);

        global $DB, $PAGE;
        $PAGE->requires->jquery();

        echo '
            <div id="myModal" class="modal" style="display: none;">
                <div class="modal-content" style="background-color: #fff; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 80%; max-width: 500px; border-radius: 10px;">
                    <span class="close" style="color: #aaa; float: right; font-size: 28px; font-weight: bold;"></span>
                    <div class="modal-header" style="font-size: 1.5em; margin-bottom: 10px;">Detalhes</div>
                    <div class="modal-body">
                        <strong>Nome:</strong> <span id="modal-name"></span><br>
                        <strong>Função:</strong> <span id="modal-funcao"></span><br>
                        <strong>Observação:</strong> <span id="modal-obs"></span>
                    </div>
                </div>
            </div>';

        // Inclui o código JavaScript necessário para o modal
        $PAGE->requires->js_init_code("
        $(document).ready(function() {
            $('.openModalLink').on('click', function(e) {
                e.preventDefault();
                var id = $(this).data('id');

                // Chamada AJAX para buscar os dados do usuário
                $.ajax({
                    url: M.cfg.wwwroot + '/webservice/rest/server.php',
                    method: 'GET',
                    dataType: 'json',
                    data: {
                        wstoken: '579075239c632c2e2adb4a275b7a1daf',
                        wsfunction: 'block_reportfoco_get_obs',
                        moodlewsrestformat: 'json',
                        id: id
                    },
                    success: function(response) {
                        if (response) {
                            // Preenche os dados no modal
                            $('#modal-name').text(response.name);
                            $('#modal-funcao').text(response.funcao_usr);
                            $('#modal-obs').html(response.obs); // `html` para manter a formatação
                            // Exibe o modal
                            $('#myModal').fadeIn();
                        } else {
                            alert('Dados não encontrados.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Erro ao buscar dados:', error);
                        alert('Erro ao buscar os dados.');
                    }
                });
            });

            // Função para fechar o modal
            $('.close').on('click', function() {
                $('#myModal').fadeOut();
            });

            // Fecha o modal se o usuário clicar fora dele
            $(window).on('click', function(e) {
                if ($(e.target).is('#myModal')) {
                    $('#myModal').fadeOut();
                }
            });
        });
    ");

    echo '<div id="infoModal" class="modal" style="display: none;">
    <div class="modal-content" style="background-color: #fff; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 80%; max-width: 500px; border-radius: 10px;">
        <span class="close" style="color: #aaa; float: right; font-size: 28px; font-weight: bold;"></span>
        <div class="modal-header" style="font-size: 1.5em; margin-bottom: 10px;">Informações do Professor</div>
        <div class="modal-body">
        <p><strong>Nome Professor:</strong> <span id="info-name"></span></p>
            <p><strong>Semibarra:</strong> <span id="info-semibarra"></span></p>
            <p><strong>Semitijuca:</strong> <span id="info-semitijuca"></span></p>
            <p><strong>Profonline:</strong> <span id="info-profonline"></span></p>
            <p><strong>Profprojetos:</strong> <span id="info-profprojetos"></span></p>
            <p><strong>Total:</strong> <span id="info-total"></span></p>
        </div>
    </div>
</div>
    ';

    $PAGE->requires->js_init_code("$(document).ready(function() {
    $('.openInfoModalLink').on('click', function(e) {
        e.preventDefault();

        // Captura os valores dos atributos data-
        var name = $(this).data('name');
        var semibarra = $(this).data('semibarra');
        var semitijuca = $(this).data('semitijuca');
        var profonline = $(this).data('profonline');
        var profprojetos = $(this).data('profprojetos');
        var total = $(this).data('total');

        // Preenche os dados no modal
        $('#info-name').text(name);
        $('#info-semibarra').text(semibarra > 0 ? 'R$ ' + semibarra.toFixed(2).replace('.', ',') : 'R$ 0,00');
        $('#info-semitijuca').text(semitijuca > 0 ? 'R$ ' + semitijuca.toFixed(2).replace('.', ',') : 'R$ 0,00');
        $('#info-profonline').text(profonline > 0 ? 'R$ ' + profonline.toFixed(2).replace('.', ',') : 'R$ 0,00');
        $('#info-profprojetos').text(profprojetos > 0 ? 'R$ ' + profprojetos.toFixed(2).replace('.', ',') : 'R$ 0,00');
        $('#info-total').text(total);

        // Exibe o modal
        $('#infoModal').fadeIn();
    });

    // Função para fechar o modal
    $('.close').on('click', function() {
        $('#infoModal').fadeOut();
    });

    // Fecha o modal se o usuário clicar fora dele
    $(window).on('click', function(e) {
        if ($(e.target).is('#infoModal')) {
            $('#infoModal').fadeOut();
        }
    });
});");

        $this->mes = $mes;
        $this->ano = $ano_escolhido;
        $this->funcao = $exfuncao_escolhido;


        // Define as colunas e os cabeçalhos da tabela
        $columns = ['id', 'name', 'questao', 'correcao_questao', 'redacao', 'quimica', 'biologia', 'professor', 'assistente', 'monitor','mchat', 'estudio', 'aplicador', 'colaborador', 'colaboradorserv', 'total', 'acao'];
        $headers = ['ID', 'Nome Completo', 'Banca Foco Medicina', 'Correção Questão', 'Correção Redação', 'Correção química', 'Correção biologia', 'Professor Titular', 'Professor Assistente', 'Monitor','Monitor Chat', 'Gravações de aula', 'Aplicador de Simulado', 'Colaborador Contratado', 'Colaborador Serviço', 'Total', 'Ação'];

        $this->define_columns($columns);
        $this->define_headers($headers);

        $this->sortable(true, 'name', SORT_ASC);
        $this->collapsible(false);

        $params = [];
        GLOBAL $DB;
        // Adiciona a filtragem por mês, se fornecido
        if ($mes and $ano_escolhido) {
            $params['mes'] = $mes;
            $params['ano'] = $ano_escolhido;
            
            if ($exfuncao_escolhido !== 'all') {
                $params['funcao'] = $exfuncao_escolhido;
                $exfuncao = " AND f.funcao_usr = :funcao";

            }
            
            $sql = "SELECT * FROM {block_reportfoco_funcoes} f JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao WHERE mes = ? AND YEAR(FROM_UNIXTIME(q.timecreated)) = ? GROUP BY q.idfuncao, q.funcao";
            $consultames = $DB->get_records_sql($sql, $params);
            if ($consultames) {

                $fields = "f.id,
                f.name,
                f.questao,
                f.correcao_questao,
                f.redacao,
                f.quimica,
                f.biologia,
                f.professor,
                f.assistente,
                f.monitor,
                f.estudio,
                f.aplicador,
                f.colaborador,
                f.colaboradorserv,
                f.mchat,
                SUM(CASE WHEN q.funcao = 'questao' THEN q.qtd ELSE 0 END) AS questao_qtd,
                SUM(CASE WHEN q.funcao = 'redacao' THEN q.qtd ELSE 0 END) AS redacao_qtd,
                SUM(CASE WHEN q.funcao = 'quimica' THEN q.qtd ELSE 0 END) AS quimica_qtd,
                SUM(CASE WHEN q.funcao = 'biologia' THEN q.qtd ELSE 0 END) AS biologia_qtd,
                SUM(CASE WHEN q.funcao = 'professor' THEN q.qtd ELSE 0 END) AS professor_qtd,
                SUM(CASE WHEN q.funcao = 'assistente' THEN q.qtd ELSE 0 END) AS assistente_qtd,
                SUM(CASE WHEN q.funcao = 'monitor' THEN q.qtd ELSE 0 END) AS monitor_qtd,
                SUM(CASE WHEN q.funcao = 'estudio' THEN q.qtd ELSE 0 END) AS estudio_qtd,
                SUM(CASE WHEN q.funcao = 'aplicador' THEN q.qtd ELSE 0 END) AS aplicador_qtd,
                SUM(CASE WHEN q.funcao = 'colaborador' THEN q.qtd ELSE 0 END) AS colaborador_qtd,
                SUM(CASE WHEN q.funcao = 'colaboradorserv' THEN q.qtd ELSE 0 END) AS colaboradorserv_qtd,
                SUM(CASE WHEN q.funcao = 'mchat' THEN q.qtd ELSE 0 END) AS mchat_qtd,
                f.questao_valor,
                f.correcao_questao_valor,
                f.redacao_valor,
                f.quimica_valor,
                f.biologia_valor,
                f.professor_valor,
                f.assistente_valor,
                f.monitor_valor,
                f.estudio_valor,
                f.aplicador_valor,
                f.colaborador_valor,
                f.colaboradorserv_valor,
                f.mchat_valor,
                q.mes,
                q.idfuncao";

                $where .= "f.status = 1" . $exfuncao ."
                AND (
                    (q.mes = :mes OR q.mes IS NULL) AND YEAR(FROM_UNIXTIME(q.timecreated)) = :ano
                    OR (
                        f.questao = 1 OR f.correcao_questao OR f.redacao = 1 OR f.quimica = 1 OR f.biologia = 1
                        OR f.professor = 1 OR f.assistente = 1 OR f.monitor = 1 OR f.estudio = 1
                        OR f.aplicador = 1 OR f.colaborador = 1 OR f.mchat = 1
                    )
                )
                GROUP BY f.id, f.name";
            } else {

                $fields = "f.id,
                f.name,
                f.questao,
                f.correcao_questao,
                f.redacao,
                f.quimica,
                f.biologia,
                f.professor,
                f.assistente,
                f.monitor,
                f.estudio,
                f.aplicador,
                f.colaborador,
                f.colaboradorserv,
                f.mchat,
                f.questao_valor,
                f.redacao_valor,
                f.quimica_valor,
                f.biologia_valor,
                f.professor_valor,
                f.assistente_valor,
                f.monitor_valor,
                f.estudio_valor,
                f.aplicador_valor,
                f.colaborador_valor,
                f.colaboradorserv_valor,
                f.mchat_valor,
                q.mes,
                q.idfuncao";

                $where .= "f.status = 1" . $exfuncao ."
                AND (
                    (q.mes = :mes OR q.mes IS NULL) AND YEAR(FROM_UNIXTIME(q.timecreated)) = :ano
                    OR (
                        f.questao = 1 OR f.correcao_questao OR f.redacao = 1 OR f.quimica = 1 OR f.biologia = 1
                        OR f.professor = 1 OR f.assistente = 1 OR f.monitor = 1 OR f.estudio = 1
                        OR f.aplicador = 1 OR f.colaborador = 1 OR f.mchat = 1
                    )
                )";
            }
        }

        $from = "{block_reportfoco_funcoes} f LEFT JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao";

        $this->set_sql($fields, $from, $where, $params);

    }

    function col_total($values)
    {
        GLOBAL $DB;
        $total = 0;

        // Lista de todas as funções possíveis
        $funcoes = ['questao', 'correcao_questao', 'redacao', 'quimica', 'biologia', 'professor', 'assistente', 'monitor', 'estudio', 'aplicador', 'colaborador', 'colaboradorserv', 'mchat', 'semibarra', 'semitijuca', 'profonline', 'profprojetos'];

        // Percorre cada função para calcular o total
        foreach ($funcoes as $funcao) {
            $qtd_field = $funcao . '_qtd';
            $valor_field = $funcao . '_valor';

            // Se a função for 'colaborador', apenas soma o valor, caso contrário multiplica pela quantidade
        if (($funcao === 'colaborador' && $values->colaborador == 1) ||
            ($funcao === 'assistente' && $values->assistente == 1)) {

                if (!empty ($values->$valor_field)) {
                    // Substitui vírgula por ponto para conversão de tipo e soma ao total
                    $valor = str_replace(',', '.', $values->$valor_field);
                    $total += (float) $valor;

                }
            }
        }

        $params['mes'] = $this->mes;
        $params['ano'] = $this->ano;
        $params['idfuncao'] = $values->id;

        $sql = "SELECT * FROM {block_reportfoco_funcoes} f
        JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao
        WHERE mes = ? AND YEAR(FROM_UNIXTIME(q.timecreated)) = ? and idfuncao = ? and f.status = 1";
        $consultames = $DB->get_records_sql($sql, $params);

        foreach ( $consultames as  $consultame) {

        if (isset ($consultame->qtd) && !empty ($consultame->valor_mes)) {

            // Substitui vírgula por ponto para conversão de tipo e calcula o subtotal
            $valor = str_replace(',', '.', $consultame->valor_mes);
            $total += $consultame->qtd * (float) $valor;

        }
    }
        $this->total_acumulado += $total;
        // Se o total for maior que zero, formata o valor para moeda, senão retorna vazio
        return $total > 0 ? 'R$ ' . number_format($total, 2, ',', '.') : null;
    }

    public function finish_output($closeexportclassdoc = true) {
        if ($this->total_acumulado > 0) {
            echo "<div><span style='font-weight: bold;'>Valor Geral do Mês:</span> R$ " . number_format($this->total_acumulado, 2, ',', '.') . "</div>";
        }
        parent::finish_output($closeexportclassdoc); // Certifique-se de passar o argumento para a chamada do método pai
    }

    function col_name($values) {

        // Inclui o ID no atributo data-id para ser acessado pelo JavaScript
        return '<a href="#" class="openModalLink" data-id="' . $values->id . '">' . $values->name . '</a>';
    }



    function col_questao($values)
    {
        return $this->format_checkmark($values->questao_qtd, 'questao', $values);
    }

    function col_correcao_questao($values)
    {
        return $this->format_checkmark($values->correcao_questao_qtd, 'correcao_questao', $values);
    }

    function col_redacao($values)
    {
        return $this->format_checkmark($values->redacao_qtd, 'redacao', $values);
    }

    function col_quimica($values)
    {
        return $this->format_checkmark($values->quimica_qtd, 'quimica', $values);
    }

    function col_biologia($values)
    {
        return $this->format_checkmark($values->biologia_qtd, 'biologia', $values);
    }

    function col_assistente($values)
    {
        return $this->format_checkmark($values->assistente_qtd, 'assistente', $values);
    }
    function col_monitor($values)
    {
        return $this->format_checkmark($values->monitor_qtd, 'monitor', $values);
    }
    function col_professor($values)
    {
        GLOBAL $DB;

        $params['mes'] = $this->mes;
        $params['ano'] = $this->ano;
        $params['idfuncao'] = $values->id;

        $sql = "SELECT * FROM {block_reportfoco_funcoes} f
                JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao
                WHERE mes = ? AND YEAR(FROM_UNIXTIME(q.timecreated)) = ? and idfuncao = ? and f.status = 1";
        $consultames = $DB->get_records_sql($sql, $params);

        $total = 0;
        $showLink = false;

        $semibarraValor = 0;
        $semitijucaValor = 0;
        $profonlineValor = 0;
        $profprojetosValor = 0;

        foreach ($consultames as $consultame) {
            

            if ($consultame->semibarra == 1 || $consultame->semitijuca == 1 || $consultame->profonline == 1 || $consultame->profprojetos == 1) {
                if (isset($consultame->qtd) && !empty($consultame->valor_mes)) {
                    $valor = str_replace(',', '.', $consultame->valor_mes);

                    if ($consultame->semibarra == 1 && $consultame->funcao == 'semibarra') {
                        $semibarraValor = $consultame->qtd * (float)$valor;
                    }

                    if ($consultame->semitijuca == 1 && $consultame->funcao == 'semitijuca') {
                        $semitijucaValor = $consultame->qtd * (float)$valor;
                    }

                    if ($consultame->profonline == 1 && $consultame->funcao == 'profonline') {
                        $profonlineValor = $consultame->qtd * (float)$valor;
                    }

                    if ($consultame->profprojetos == 1 && $consultame->funcao == 'profprojetos') {
                        $profprojetosValor = $consultame->qtd * (float)$valor;
                    }

                    $total += $consultame->qtd * (float)$valor;
                    $showLink = true;
                }
            }
        }

        if ($showLink) {
            $formattedValue = 'R$ ' . number_format($total, 2, ',', '.');

            // Cria o link para o modal com os valores individuais e o total
            return '<a href="#" class="openInfoModalLink" 
                    data-name="' . $values->name . '"
                    data-semibarra="' . $semibarraValor . '" 
                    data-semitijuca="' . $semitijucaValor . '" 
                    data-profonline="' . $profonlineValor . '" 
                    data-profprojetos="' . $profprojetosValor . '" 
                    data-total="' . $formattedValue . '">' . $formattedValue . '</a>';
        } else {
            if($consultame->funcao == 'professor'){ 
                return $this->format_checkmark($values->professor_qtd, 'professor', $values);
            }else{
                if($consultame->profprojetos == 1 || $consultame->profonline || $consultame->semitijuca == 1 || $consultame->semibarra == 1){
                    return 'R$ 0,00';
                }else{
            
                return 'X';
                }
            }
        }
    }
    
    function col_estudio($values)
    {
        return $this->format_checkmark($values->estudio_qtd, 'estudio', $values);
    }
    function col_aplicador($values)
    {
        return $this->format_checkmark($values->aplicador_qtd, 'aplicador', $values);
    }
    function col_colaborador($values)
    {
        return $this->format_checkmark($values->colaborador_qtd, 'colaborador', $values);
    }

    function col_colaboradorserv($values)
    {
        return $this->format_checkmark($values->colaboradorserv_qtd, 'colaboradorserv', $values);
    }

    function col_mchat($values)
    {
        return $this->format_checkmark($values->mchat_qtd, 'mchat', $values);
    }

    private function format_checkmark($value, $funcao, $values) {

        GLOBAL $DB;
        // Se o valor for maior que zero, exibe o checkmark
        if($values->$funcao == 1){
            $checkmark = '✓';

        }

        if (($funcao === 'colaborador' && !empty($values->colaborador_valor) && $values->colaborador == 1) || ($funcao === 'assistente' && !empty($values->assistente_valor) && $values->assistente == 1)) {
            // Para as funções colaborador e monitor, exibe apenas o valor sem multiplicar pela quantidade
            $valor = str_replace(',', '.', $values->{$funcao . '_valor'});
            $valor_formatado = number_format(floatval($valor), 2, ',', '.');
            return $checkmark . "<br>R$ " . $valor;
        } else if ($checkmark) {
            $params['mes'] = $this->mes;
            $params['ano'] = $this->ano;
            $params['idfuncao'] = $values->id;
            $params['funcao'] = $funcao;
            $sql = "SELECT * FROM  {block_reportfoco_funcoes} f
            JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao
            WHERE q.mes = ? AND YEAR(FROM_UNIXTIME(q.timecreated)) = ? and q.idfuncao = ? and q.funcao = ? and f.status = 1 GROUP BY idfuncao, funcao";
            $consultvalormes = $DB->get_record_sql($sql, $params);
            
        //     if($values->id == 71){
        //   print_r( $consultvalormes);
        //     print_r($funcao);
        //     print_r($values->mes);
        //     print_r($this->mes);
        //     }
            

            if($funcao === 'colaboradorserv' && $consultvalormes->mes == $this->mes){
                return "<br>R$ " . $consultvalormes->valor_mes;
            }

            if($funcao === 'colaboradorserv' && $values->mes <> $this->mes){
                return "X";
            }

            $qtd_text = isset($consultvalormes->qtd) ? "Feito:<br>{$consultvalormes->qtd}" : "";

            // Converte o valor para float antes de formatá-lo
            $valor = isset($consultvalormes->valor_mes) ? floatval(str_replace(',', '.', $consultvalormes->valor_mes)) : 0;
            $total_mes = $valor * $consultvalormes->qtd;
            $valor_text = "R$ " . number_format($total_mes, 2, ',', '.');
            if($qtd_text){
                return "{$valor_text}";
            }else{
                return "{$valor_text}";
            }
        } else {
            return "X";
        }
    }



    function col_acao($values) {
        // Verifique se há um registro para o usuário antes de exibir os botões
        if (!empty($values->id) && $values->id > 0) {
            $editUrl = new moodle_url('/blocks/reportfoco/edit_user.php', array('id' => $values->id, 'mes' => $this->mes, 'sesskey' => sesskey()));
            $editLink = html_writer::link($editUrl, 'Editar', array('class' => 'btn btn-primary btn-sm'));

            $deleteUrl = new moodle_url('/blocks/reportfoco/management.php', array('delete' => $values->id, 'sesskey' => sesskey()));
            $deleteLink = html_writer::link($deleteUrl, 'Excluir', array('class' => 'btn btn-danger btn-sm'));

            // Agrupa os botões para alinhamento responsivo
            return html_writer::div($editLink . ' ' . $deleteLink, 'btn-group');
        } else {
            // Se não houver registro, retorna null para que nada seja exibido
            return null;
        }
    }

}

function display_month_selector_and_table() {
    global $PAGE, $CFG;

    require_once($CFG->dirroot.'/blocks/reportfoco/selector_form.php'); // Ajuste o caminho conforme necessário

    $mform = new selector_form();
    
    $PAGE->requires->js_init_code("
        var head = document.head;
        var style = document.createElement('style');
        style.type = 'text/css';
        style.appendChild(document.createTextNode(`
            .reportfoco-table-wrapper {
    
            }
            .reportfoco-table-wrapper .no-overflow {
                overflow: visible;
            }
            .flexible thead th {
                position: sticky;
                top: 0;
                z-index: 1052;
                background-color: #ffff;
            }
        `));
        head.appendChild(style);
    // JavaScript para a caixa de busca
    document.addEventListener('DOMContentLoaded', function() {
        var searchBox = document.getElementById('searchBox');
        searchBox.addEventListener('keyup', function(e) {
            var searchTerm = e.target.value.toLowerCase();
            var table = document.querySelector('.reportfoco-table-wrapper table');
            Array.from(table.getElementsByTagName('tr')).forEach(function(row) {
                var cells = row.getElementsByTagName('td');
                if (cells.length > 0) {
                    var name = cells[1].textContent.toLowerCase(); // ajuste o índice conforme necessário
                    row.style.display = name.includes(searchTerm) ? '' : 'none';
                }
            });
        });
    });
");


    if ($fromform = $mform->get_data()) {
        $mform->display();
        // Dados submetidos disponíveis, use $fromform->mes e $fromform->ano para filtrar a tabela
        $mes_escolhido = $fromform->mes;
        $ano_escolhido = $fromform->ano;
        $exfuncao_escolhido = $fromform->exfuncao;

        // Crie e configure a tabela com base nos dados submetidos
        $table = new reportfoco_funcoes_table('uniqueid_reportfoco_funcoes', $mes_escolhido, $ano_escolhido, $exfuncao_escolhido);
        $table->define_baseurl($PAGE->url->out());
        echo '<br>';
        echo '<input type="text" id="searchBox" placeholder="Buscar por nome..." size="40">';

        echo '<div class="reportfoco-table-wrapper">';
        $table->out(0, false); // '30' é o número de linhas por página
        echo '</div>';
    } else {
        // Exibe o formulário
        $mform->display();
    }
}



function get_check_coord() {
    global $USER, $DB;

    // Pega os nomes dos departamentos, que também são os nomes das colunas
    $departmentColumns = explode(';', $USER->department);


    $whereClauses = array();
    foreach ($departmentColumns as $columnName) {
        $columnName = trim($columnName);
        if (!empty($columnName)) {
            $whereClauses[] = $columnName . " = ?";
        }
    }


    $whereSql = implode(' OR ', $whereClauses);


    if (!empty($whereSql)) {
        $records = $DB->get_records_select('block_reportfoco_funcoes', $whereSql, array_fill(0, count($whereClauses), 1));
    } else {
        $records = array();
    }

    // Retorna os registros encontrados, se houver
    return $records;
}


function display_reportfoco_qtd_table($mes) {
    global $DB, $OUTPUT, $PAGE, $USER;

    $mes_escolhido = $mes ? $mes : date('n');

    // Divide a string de departamentos em um array
    $departments = explode(';', $USER->department);
    $ano_vigente = date('Y');

    // Cria uma instância da tabela.
    $table = new table_sql('uniqueid_reportfoco_qtd');

    // Define as colunas e cabeçalhos.
    $columns = array('name', 'funcao', 'qtd');
    $headers = array('Nome', 'Funcao', 'Quantidade');

    $table->define_columns($columns);
    $table->define_headers($headers);
    $table->sortable(true, 'name', SORT_ASC);
    $table->collapsible(false);

    // Prepara a cláusula IN para a consulta SQL com os placeholders corretos para cada departamento
    $placeholders = implode(',', array_fill(0, count($departments), '?'));

    // Ajusta a cláusula WHERE para utilizar placeholders
    $where = "mes = ? AND q.funcao IN ($placeholders) AND YEAR(FROM_UNIXTIME(q.timecreated)) = ?";
    $params = array_merge([$mes_escolhido], $departments, [$ano_vigente]);

    $fields = '*';
    $from = "{block_reportfoco_funcoes} f JOIN {block_reportfoco_qtd} q ON f.id = q.idfuncao";

    $table->set_sql($fields, $from, $where, $params);

    // Define a URL base da tabela, necessária para a ordenação e paginação.
    $table->define_baseurl($PAGE->url);

    $months = [
        1 => 'Janeiro',
        2 => 'Fevereiro',
        3 => 'Março',
        4 => 'Abril',
        5 => 'Maio',
        6 => 'Junho',
        7 => 'Julho',
        8 => 'Agosto',
        9 => 'Setembro',
        10 => 'Outubro',
        11 => 'Novembro',
        12 => 'Dezembro'
    ];

    // Constrói o formulário de seleção de mês.
    $select = new single_select(new moodle_url('index.php'), 'mes', $months, $mes_escolhido, null);
    $select->label = 'Selecione o mês';

    $PAGE->requires->js_init_code("
        var head = document.head;
        var style = document.createElement('style');
        style.type = 'text/css';
        style.appendChild(document.createTextNode(`
            .reportfoco-table-wrapper {
    
            }
            .reportfoco-table-wrapper .no-overflow {
                overflow: visible;
            }
            .flexible thead th {
                position: sticky;
                top: 0;
                z-index: 1052;
                background-color: #ffff;
            }
        `));
        head.appendChild(style);

        // JavaScript para a caixa de busca
        document.addEventListener('DOMContentLoaded', function() {
            var searchBox = document.getElementById('searchBox');
            searchBox.addEventListener('keyup', function(e) {
                var searchTerm = e.target.value.toLowerCase();
                var table = document.querySelector('.reportfoco-table-wrapper table');
                Array.from(table.getElementsByTagName('tr')).forEach(function(row) {
                    var cells = row.getElementsByTagName('td');
                    if (cells.length > 0) {
                        var name = cells[0].textContent.toLowerCase(); // ajuste o índice conforme necessário
                        row.style.display = name.includes(searchTerm) ? '' : 'none';
                    }
                });
            });
        });
    ");

    echo $OUTPUT->render($select);

    echo '<br>Nome de Colaborador';
    echo '<input type="text" id="searchBox" placeholder="Buscar por nome..." size="40">';

    echo '<div class="reportfoco-table-wrapper">';
    $table->out(0, false); // '0' é o número de linhas por página, 0 significa sem paginação
    echo '</div>';
}




class reportfoco_funcoes_table_list extends table_sql
{
    function __construct($uniqueid)
    {
        parent::__construct($uniqueid);

        // Define as colunas e os cabeçalhos da tabela
        $columns = array('id', 'name', 'questao', 'correcao_questao', 'redacao', 'quimica', 'biologia', 'professor', 'assistente', 'monitor', 'estudio', 'aplicador', 'colaborador', 'colaboradorserv', 'mchat', 'acao');
        $headers = ['ID', 'Nome Completo', 'Banca Foco Medicina', 'Correção Questão', 'Correção Redação', 'Correção química', 'Correção biologia', 'Professor', 'Assistente', 'Monitor', 'Gravações de aula', 'Aplicador de Simulado', 'Colaborador contratado', 'Colaborador Serviço', 'Monitor Chat', 'Ação'];

        $this->define_columns($columns);
        $this->define_headers($headers);

        $this->collapsible(false); // Desabilita a capacidade de "colapsar" as colunas

    }

    function col_questao($values)
    {
        return $this->format_checkmark($values->questao);

    }

    function col_correcao_questao($values)
    {
        return $this->format_checkmark($values->correcao_questao);

    }

    function col_redacao($values)
    {
        return $this->format_checkmark($values->redacao);
    }

    function col_quimica($values)
    {
        return $this->format_checkmark($values->quimica);
    }

    function col_biologia($values)
    {
        return $this->format_checkmark($values->biologia);
    }

    function col_assistente($values)
    {
        return $this->format_checkmark($values->assistente);
    }

    function col_monitor($values)
    {
        return $this->format_checkmark($values->monitor);
    }
    function col_mchat($values)
    {
        return $this->format_checkmark($values->mchat);
    }

    function col_professor($values)
    {
        return $this->format_checkmark($values->professor);
    }

    function col_estudio($values)
    {
        return $this->format_checkmark($values->estudio);
    }

    function col_aplicador($values)
    {
        return $this->format_checkmark($values->aplicador);
    }

    function col_colaborador($values)
    {
        return $this->format_checkmark($values->colaborador);
    }

    function col_colaboradorserv($values)
    {
        return $this->format_checkmark($values->colaboradorserv);
    }

    private function format_checkmark($value)
    {
        $checkmark = $value == 1 ? '✓' : '';
        return $checkmark;
    }

    protected function col_acao($values)
    {
        // Tenta obter o mês atual da URL ou usa o mês atual como padrão
        $mes_atual = optional_param('mes', date('n'), PARAM_INT);

        $editUrl = new moodle_url('/blocks/reportfoco/edit_user.php', array('id' => $values->id, 'mes' => $mes_atual, 'sesskey' => sesskey()));
        $editLink = html_writer::link($editUrl, 'Editar', array('class' => 'btn btn-primary btn-sm'));

        $deleteUrl = new moodle_url('/blocks/reportfoco/management.php', array('delete' => $values->id, 'sesskey' => sesskey()));
        $deleteLink = html_writer::link($deleteUrl, 'Excluir', array('class' => 'btn btn-danger btn-sm'));
        // Agrupa os botões para alinhamento responsivo
        return html_writer::div($editLink . ' ' . $deleteLink, 'btn-group');
    }
}

function get_funcao_table()
{
    global $PAGE;
    $table = new reportfoco_funcoes_table_list('uniqueid_reportfoco_funcoes');
    $table->set_sql('*', "{block_reportfoco_funcoes}", '1=1 AND status = 1');
    $table->define_baseurl($PAGE->url);

    $PAGE->requires->js_init_code("
        var head = document.head;
        var style = document.createElement('style');
        style.type = 'text/css';
        style.appendChild(document.createTextNode(`
            .reportfoco-table-wrapper {
    
            }
            .reportfoco-table-wrapper .no-overflow {
                overflow: visible;
            }
            .flexible thead th {
                position: sticky;
                top: 0;
                z-index: 1052;
                background-color: #ffff;
            }
        `));
        head.appendChild(style);
    // JavaScript para a caixa de busca
    document.addEventListener('DOMContentLoaded', function() {
        var searchBox = document.getElementById('searchBox');
        searchBox.addEventListener('keyup', function(e) {
            var searchTerm = e.target.value.toLowerCase();
            var table = document.querySelector('.reportfoco-table-wrapper table');
            Array.from(table.getElementsByTagName('tr')).forEach(function(row) {
                var cells = row.getElementsByTagName('td');
                if (cells.length > 0) {
                    var name = cells[1].textContent.toLowerCase(); // ajuste o índice conforme necessário
                    row.style.display = name.includes(searchTerm) ? '' : 'none';
                }
            });
        });
    });
");    
    echo '<div class="reportfoco-table-wrapper">';
    $table->out(0, false);
    echo '</div>';
}

 function returnFuncao(){
    GLOBAL $USER;
    $departmentsFromUser = explode(';', $USER->department);

    foreach ($departmentsFromUser as $deptName) {
    $deptNameSafe[] = clean_param($deptName, PARAM_ALPHANUMEXT);
    }

    return $deptNameSafe;
}
