<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file reportfocoadduser_form
 *
 * @package    block_reportfoco
 * @copyright  2024 Raphael Enes <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


 require_once("$CFG->libdir/formslib.php");

 class reportfocoAdduser_form extends moodleform {
    public function definition() {
        global $PAGE;

        $mform = $this->_form;

        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        $mform->addElement('text', 'name', 'Nome Completo:', 'size="40"');
        $mform->setType('name', PARAM_TEXT);
        $mform->addRule('name', null, 'required', null, 'client');

        $mform->addElement('text', 'funcao_usr', 'Função', 'maxlength="100" size="40"');

        // Adicionando caixas de seleção para as funções
        $fields = [
            'questao' => 'Questão',
            'correcao_questao' => 'Correção Questão',
            'redacao' => 'Redação',
            'quimica' => 'Química',
            'biologia' => 'Biologia',
            //'professor' => 'Professor',
            'assistente' => 'Assistente',
            'monitor' => 'Monitor',
            'estudio' => 'Estúdio',
            'aplicador' => 'Aplicador',
            'colaborador' => 'Colaborador',
            'colaboradorserv' => 'Colaboradorserv',
            'mchat' => 'Monitor chat',
            'semibarra' => 'Professor Semi Barra',
            'semitijuca' => 'Professor Semi Tijuca',
            'profonline' => 'Professor Online',
            'profprojetos' => 'Professor Projetos'
        ];

        $statusoptions = [
            0 => 'Inativo',
            1 => 'Ativo'
        ];

        foreach ($fields as $fieldname => $displayname) {
            $mform->addElement('select', $fieldname, $displayname, $statusoptions);
            $mform->setDefault($fieldname, 0); // Define o padrão como 'Inativo'

            // Para cada caixa de seleção, adicionamos um campo de texto para o valor
            $mform->addElement('text', $fieldname . '_valor', $displayname . ' Valor:');
            $mform->setType($fieldname . '_valor', PARAM_TEXT);
            $mform->hideIf($fieldname . '_valor', $fieldname, 'eq', 0);

            // Adiciona campo de e-mail para a função "Correção Questão"
            if ($fieldname === 'correcao_questao') {
                $mform->addElement('text', 'correcao_questao_email', 'E-mail do Colaborador:', 'maxlength="100" size="40"');
                $mform->setType('correcao_questao_email', PARAM_EMAIL);
                $mform->hideIf('correcao_questao_email', 'correcao_questao', 'eq', 0);
                $mform->addHelpButton('correcao_questao_email', 'correcao_questao_email', 'block_reportfoco');
            }
        }

        // Definindo os valores padrão para os campos de valor
        //$mform->setDefault('professor_valor', '100,00');
        $mform->setDefault('semibarra_valor', '100,00');
        $mform->setDefault('semitijuca_valor', '100,00');
        $mform->setDefault('profonline_valor', '100,00');
        $mform->setDefault('profprojetos_valor', '100,00');
        $mform->setDefault('questao_valor', '17,50');
        $mform->setDefault('redacao_valor', '6,00');
        $mform->setDefault('mchat_valor', '35,00');
        $mform->setDefault('assistente_valor', '2200.00');
        $mform->setDefault('monitor_valor', '500,00');
        $mform->setDefault('quimica_valor', '6,00');
        $mform->setDefault('biologia_valor', '6,00');
        $mform->setDefault('aplicador_valor', '75,00');
        $mform->setDefault('correcao_questao_valor', '2,00');
        $mform->setDefault('colaborador_valor', '50,00');
        $mform->setDefault('colaboradorserv_valor', '50,00');

        $mform->addElement('textarea','obs','Obeservações');

        $mform->addElement('submit', 'submitbutton', 'Salvar');
        $mform->addElement('cancel', 'cancelbutton', 'Cancelar');
    }
}


 class reportfocoAddqtd_form extends moodleform {
    private $customdata;
    private $departmentOptions; // Adiciona uma nova propriedade para armazenar as opções do departamento

    public function __construct($customdata = null, $action = null, $custom = null, $ajax = null) {
        $this->customdata = $customdata;
        $this->prepare_department_options(); // Prepara as opções de departamento
        parent::__construct($action, $custom, $ajax);
    }

    private function prepare_department_options() {
        global $DB, $USER;

        $id = $this->customdata['id']; // Asegura que temos o ID
        $departmentsFromUser = explode(';', $USER->department);
        $departmentsToShow = [];

        if (!empty($id)) {
            foreach ($departmentsFromUser as $deptName) {
                if (!empty($deptName)) {
                    $deptNameSafe = clean_param($deptName, PARAM_ALPHANUMEXT);
                    $sql = "SELECT id FROM {block_reportfoco_funcoes} WHERE id = ? AND {$deptNameSafe} = 1";

                    if ($DB->record_exists_sql($sql, [$id])) {
                        $departmentsToShow[$deptNameSafe] = ucfirst($deptNameSafe); // Capitaliza o nome do departamento
                    }
                }
            }
        }

        $this->departmentOptions = $departmentsToShow; // Armazena as opções na propriedade da classe
    }

    public function definition() {
        global $PAGE, $DB, $USER;

        $mform = $this->_form;


        // Campo oculto para ID
        $mform->addElement('hidden', 'id', isset($this->customdata['id']) ? $this->customdata['id'] : '');
        $mform->setType('id', PARAM_INT);

 if (!empty($this->departmentOptions)) {
            $mform->addElement('select', 'department', 'Escolha sua Área', $this->departmentOptions);
            $mform->setType('department', PARAM_ALPHANUMEXT);
            $mform->addRule('department', get_string('required'), 'required', null, 'client');

            if($this->departmentOptions['questao'] == 'Questao'){
                $mform->addElement('advcheckbox', 'elaboracao_questao'.$this->customdata['id'], 'Elaboração de Questão');
                $mform->setType('elaboracao_questao', PARAM_BOOL);

            }
        }


        // Campo para quantidade
        $mform->addElement('text', 'qtd', 'Quantidade', 'maxlength="10" size="10"');
        $mform->setType('qtd', PARAM_TEXT);
        $mform->addRule('qtd', get_string('required'), 'required', null, 'client');

        // Obtém o mês atual
        $currentMonth = (int)date('n'); // 'n' retorna o número do mês sem zeros à esquerda

        // Mapeamento de número do mês para nome do mês
        $monthNames = [
            1 => 'Janeiro', 2 => 'Fevereiro', 3 => 'Março',
            4 => 'Abril', 5 => 'Maio', 6 => 'Junho',
            7 => 'Julho', 8 => 'Agosto', 9 => 'Setembro',
            10 => 'Outubro', 11 => 'Novembro', 12 => 'Dezembro'
        ];

        //   $monthNames = [
        //       2 => 'Fevereiro', 3 => 'Março'
        //       ];



        // Cria a caixa de seleção para o mês com apenas o mês vigente
        $months = [$currentMonth => $monthNames[$currentMonth]];
        $mform->addElement('select', 'month', 'Mês',   $months); //$monthNames
        $mform->addRule('month', get_string('required'), 'required', null, 'client');
        $mform->setType('month', PARAM_INT);

        // Define o mês atual como o valor padrão (neste caso, é a única opção disponível)
        $mform->setDefault('month', $currentMonth);


        $months = [
            1 => 'Adicionar',0 => 'Remover'
        ];
        $mform->addElement('select', 'select', 'Escolha', $months);
        $mform->addRule('select', get_string('required'), 'required', null, 'client');
        $mform->setType('select', PARAM_INT);


        // Botões de ação
        $mform->addElement('submit', 'submitbutton', 'Salvar');
        $mform->addElement('cancel', 'cancelbutton', 'Cancelar');

        // Adiciona JavaScript customizado para controlar a lógica das checkboxes
        $PAGE->requires->js_init_code($this->add_checkbox_logic());
    }

    private function add_checkbox_logic() {
        return <<<JS
        document.addEventListener('DOMContentLoaded', function() {
            var removeCheckbox = document.querySelector('[name="remove"]');
            var addCheckbox = document.querySelector('[name="add"]');

            removeCheckbox.addEventListener('change', function() {
                addCheckbox.disabled = this.checked;
            });

            addCheckbox.addEventListener('change', function() {
                removeCheckbox.disabled = this.checked;
            });
        });
        JS;
    }
}

/**
 * Formulário para registrar atividades do monitor em cursos
 */
class reportfocoMonitorForm extends moodleform {
    private $customdata;

    public function __construct($customdata = null, $action = null, $custom = null, $ajax = null) {
        $this->customdata = $customdata;
        parent::__construct($action, $custom, $ajax);
    }

    public function definition() {
        global $PAGE, $DB, $USER;

        $mform = $this->_form;

        // Campo oculto para o ID do curso
        $mform->addElement('hidden', 'courseid', isset($this->customdata['courseid']) ? $this->customdata['courseid'] : '');
        $mform->setType('courseid', PARAM_INT);

        // Obtém o mês selecionado da URL ou dos dados do formulário
        $selectedMonth = isset($this->customdata['selectedmonth']) ? $this->customdata['selectedmonth'] : date('n');

        // Campo oculto para o mês
        $mform->addElement('hidden', 'month', $selectedMonth);
        $mform->setType('month', PARAM_INT);

        // Obtém a quantidade de fóruns respondidos automaticamente
        global $CFG;
        require_once($CFG->dirroot . '/blocks/reportfoco/monitor_courses.php');
        $forumCount = count_forum_posts_by_user($this->customdata['courseid'], $USER->id, $selectedMonth);

        // Campo para quantidade de fóruns respondidos (somente leitura)
        $mform->addElement('static', 'foruns_respondidos_label', 'Quantidade de Respostas nos Fóruns:', $forumCount);
        $mform->addElement('hidden', 'foruns_respondidos', $forumCount);
        $mform->setType('foruns_respondidos', PARAM_INT);

        // Campo para tempo gasto respondendo fóruns (em minutos)
        $mform->addElement('text', 'tempo_resposta', 'Tempo gasto respondendo (minutos):', 'maxlength="10" size="10"');
        $mform->setType('tempo_resposta', PARAM_INT);
        // Adiciona regra de validação condicional - só é obrigatório quando não estiver consultando
        $mform->addRule('tempo_resposta', get_string('required'), 'required', null, 'client');

        // Adiciona instruções sobre como remover tempo
        $mform->addElement('static', 'tempo_instrucoes', '',
            '<div class="alert alert-info">
                <strong>Instruções:</strong><br>
                - Para adicionar tempo, insira um valor positivo (ex: 30)<br>
                - Para remover tempo, insira um valor negativo (ex: -30)
            </div>');

        // Adiciona um campo estático para mostrar o tempo total acumulado
        if (isset($this->customdata['courseid'])) {
            $params = [
                'userid' => $USER->id,
                'courseid' => $this->customdata['courseid'],
                'mes' => $selectedMonth
            ];

            $existingRecord = $DB->get_record('block_reportfoco_monitor', $params);
            $tempoTotal = $existingRecord ? $existingRecord->tempo_resposta : 0;

            $mform->addElement('static', 'tempo_total', 'Tempo total acumulado neste mês:',
                '<span class="badge badge-success">' . $tempoTotal . ' minutos</span>');
        }

        // Já temos o seletor de mês no início do formulário, não precisamos de outro aqui

        // Botões de ação
        $buttonarray = [];
        $buttonarray[] = $mform->createElement('submit', 'submitbutton', 'Salvar');
        $buttonarray[] = $mform->createElement('cancel', 'cancelbutton', 'Cancelar');
        $mform->addGroup($buttonarray, 'buttonar', '', ' ', false);

        // Adicionamos um campo oculto para identificar qual botão foi clicado
        $mform->addElement('hidden', 'action', 'save');
        $mform->setType('action', PARAM_ALPHA);


        // Preenche os valores do formulário se já existirem registros
        if (isset($this->customdata['courseid'])) {
            // Busca na tabela block_reportfoco_monitor
            $params = [
                'userid' => $USER->id,
                'courseid' => $this->customdata['courseid'],
                'mes' => $selectedMonth
            ];

            $existingRecord = $DB->get_record('block_reportfoco_monitor', $params);

            if ($existingRecord) {
                $mform->setDefault('tempo_resposta', 0); // Sempre começa com 0 para adicionar novo tempo
            }
        }
    }

    /**
     * Validação personalizada do formulário
     *
     * @param array $data Os dados do formulário
     * @param array $files Os arquivos enviados
     * @return array Os erros de validação
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        // Se a ação for 'consult' ou o botão consultabutton foi clicado, não validamos o campo tempo_resposta
        if (isset($data['action']) && $data['action'] === 'consult' || isset($data['consultabutton'])) {
            // Remove qualquer erro relacionado ao campo tempo_resposta
            unset($errors['tempo_resposta']);
        }

        return $errors;
    }
}